package cn.microhis.invoice.plugins.baiwangjf;

import cn.com.idmy.base.exception.BizException;
import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.model.enums.BizType;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.lang.tuple.Pair;
import org.dromara.hutool.core.math.NumberUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static cn.com.idmy.base.util.Assert.notBlank;
import static cn.microhis.invoice.plugins.baiwangjf.Http.post;

/**
 * 湖南百旺金赋科技有限公司
 */
@Slf4j
public class BaiwangjfImpl extends ProviderPluginBase<BaiwangjfConfig> implements ProviderPlugin {
    static final String OK_CODE = "0000";

    public BaiwangjfImpl() {
        super("BAIWANGJF");
    }

    @Override
    protected BaiwangjfConfig parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        var cfg = new BaiwangjfConfig();
        cfg.certKey = notBlank(config.getString("certKey"), "请配置证书key");
        cfg.key = notBlank(config.getString("key"), "请配置密钥");
        cfg.agencyCode = notBlank(config.getString("agencyCode"), "请配置agencyCcode");
        cfg.mofDivCode = notBlank(config.getString("mofDivCode"), "请配置mofDivCode");
        cfg.url = notBlank(config.getString("url"), "请配置url");
        cfg.initCertFile(tenantId);
        return cfg;
    }

    public String toChargeCode(BizType bizType, String code) {
        String[] split = code.split(",");
        return switch (bizType) {
            case REGISTRATION, OUTPATIENT -> split[0];
            case INPATIENT -> split[1];
            case OTHER -> throw new BizException("不支持的开票类型");
        };
    }

    private Pair<BigDecimal, List<Project>> buildDetails(Invoice invoice) {
        var feeTypeMap = mapFeeType();
        var map = new HashMap<String, Project>();
        var totalAmt = BigDecimal.ZERO;
        var details = invoice.getDetails();
        for (int i = 0, size = details.size(); i < size; i++) {
            var detail = details.get(i);
            var feeTypeCode = detail.getFeeTypeCode();
            var project = map.get(feeTypeCode);
            if (project == null) {
                var chargeCode = toChargeCode(invoice.getBizType(), feeTypeMap.get(feeTypeCode).getCode());
                map.put(feeTypeCode, Project.builder().itemCode(chargeCode).itemName(detail.getFeeType()).itemAmount(detail.getAmt()).build());
            } else {
                project.setItemAmount(project.getItemAmount().add(detail.getAmt()));
            }
            totalAmt = totalAmt.add(detail.getAmt());
        }

        for (int i = 0, size = details.size(); i < size; i++) {
            var detail = details.get(i);
            map.get(detail.getFeeTypeCode()).getDetails().add(Detail.builder()
                    .auxItemCode(detail.getCode())
                    .auxItemName(detail.getName())
                    .auxItemQuantity(detail.getQty())
                    .auxItemUnit(detail.getUnit())
                    .auxItemStd(fmtAmt(detail.getPrice()))
                    .auxItemAmount(NumberUtil.format("#.####", detail.getAmt()))
                    .build());
        }
        return Pair.of(totalAmt, new ArrayList<>(map.values()));
    }

    private void setMi(Invoice invoice, Outpatient main) {
        main.otherPayAmount = fmtAmt(invoice.getOtherPay());
        main.ownPayAmount = fmtAmt(invoice.getCashPay());
        var mi = invoice.getMedicalInsurance();
        if (mi == null) {
            main.fundPayAmount = "0.00";
            main.accountPayAmount = "0.00";
            main.selfpaymentCost = "0.00";
            main.selfpaymentAmount = "0.00";
        } else {
            main.medicalInsuranceType = mi.getType().value().toString();
            main.medicalInsuranceId = mi.getNo();
            main.fundPayAmount = fmtAmt(mi.getFundPay());
            main.accountPayAmount = fmtAmt(mi.getAccountPay());
            main.selfpaymentAmount = fmtAmt(mi.getSelfPay());
        }
    }

    @Override
    protected Invoice createRegistration(Invoice invoice) {
        return createOutpatient(invoice);
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        var cfg = config();
        var outpatient = invoice.getOutpatient();
        var now = LocalDateTime.now();
        var main = Outpatient.builder()
                .totalAmount(fmtAmt(invoice.getAmt()))
                .issueDate(now)
                .issueTime(now.toLocalTime())
                .bizCode(invoice.getInvoiceId().toString())
                .recName(invoice.getPayee())
                .checker(invoice.getPayee())
                .invoiceType(0)
                .gender(invoice.getGender())
                .payerPartyType(invoice.getPayerType().value().toString()) // 个人
                .payerPartyCode(invoice.getCardNo())
                .handlingPerson(invoice.getPayee())
                .payerPhone(invoice.getMobile())
                .payerPartyName(invoice.getName())
                .invoicingPartyCode(cfg.agencyCode)
                .invoicingPartyName(invoice.getTenant())
                .patientNumber(outpatient.getNo())
                .medicalDate(outpatient.getVisitAt())
                .remark(invoice.getRemark())
                .build();

        main.selfpaymentCost = fmtAmt(invoice.getOwnPay());

        var pair = buildDetails(invoice);
        if (NumberUtil.equals(pair.getLeft(), invoice.getAmt())) {
            main.details = pair.getRight();
        } else {
            throw new BizException("发票总金额和结算单总金额不一致");
        }
        setMi(invoice, main);

        var data = JSONObject.of("main", JSONArray.of(main));
        var out = post(config(), data, Outpatient.PATH);
        if (!OK_CODE.equals(out.getCode()) && !"8008".equals(out.getCode())) {
            throw new BizException(out.getMsg());
        }
        invoice.setTicketCode(out.getTicketCode());
        invoice.setTicketNo(out.getTicketNo());
        invoice.setCheckCode(out.getCheckCode());
        invoice.setUrl(out.getTicketUrl());
        return invoice;
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        var cfg = config();
        var now = LocalDateTime.now();
        var main = Inpatient.builder()
                .totalAmount(fmtAmt(invoice.getAmt()))
                .issueDate(now)
                .issueTime(now.toLocalTime())
                .bizCode(invoice.getProviderKey())
                .recName(invoice.getPayee())
                .checker(invoice.getPayee())
                .invoiceType(0)
                .gender(invoice.getGender())
                .payerPartyType(invoice.getPayerType().value().toString())
                .payerPartyCode(invoice.getCardNo())
                .payerPhone(invoice.getMobile())
                .payerPartyName(invoice.getName())
                .handlingPerson(invoice.getPayee())
                .invoicingPartyCode(cfg.agencyCode)
                .invoicingPartyName(invoice.getTenant())
                .remark(invoice.getRemark())
                .build();
        var pair = buildDetails(invoice);
        if (NumberUtil.equals(pair.getLeft(), invoice.getAmt())) {
            main.details = pair.getRight();
        } else {
            throw new BizException("发票总金额和结算单总金额不一致");
        }

        main.selfpaymentCost = fmtAmt(invoice.getOwnPay());
        setMi(invoice, main);

        var inpatient = invoice.getInpatient();
        main.caseNumber = invoice.getCaseNo();
        main.hospitalizationNumber = inpatient.getNo();
        main.departmentName = inpatient.getOutDept();
        main.inHospitalDate = inpatient.getInAt();
        main.outHospitalDate = inpatient.getOutAt();
        main.prepayAmount = BigDecimal.ZERO;

        var data = JSONObject.of("main", JSONArray.of(main));
        var out = post(cfg, data, Inpatient.PATH);
        if (!OK_CODE.equals(out.getCode()) && !"8008".equals(out.getCode())) {
            throw new BizException(out.getMsg());
        }
        invoice.setTicketCode(out.getTicketCode());
        invoice.setTicketNo(out.getTicketNo());
        invoice.setCheckCode(out.getCheckCode());
        invoice.setUrl(out.getTicketUrl());
        return invoice;
    }

    Out getByKey(String key) {
        var cfg = config();
        var out = post(cfg, JSONObject.of("biz_code", key), "/meb/perinvoice/billmgr/querybillstatusinfo");
        var ticket = out.getTicket();
        if (ticket == null) {
            throw new BizException("查询电子发票异常【ticket_info为空】：" + key);
        } else {
            return ticket.toJavaObject(Out.class);
        }
    }

    @Override
    protected InvoiceReversal doReverse(Invoice in) {
        notBlank(in.getPayeeCode(), "收款人代码不能为空");
        var key = IdUtil.getSeataSnowflakeNextId();
        var reversal = Reversal.builder()
                .ticketId(in.getTicketCode() + in.getTicketNo())
                .bizCode(String.valueOf(key))
                .handlingPerson(in.getPayeeCode())
                .redReason(in.getRemark())
                .build();
        var url = in.getBizType() == BizType.INPATIENT ? "/meb/perinvoice/redbill/inhospitalredbill" : "/meb/perinvoice/redbill/outpatientredbill";
        var out = post(config(), reversal, url);
        if (!OK_CODE.equals(out.getCode()) && !"8008".equals(out.getCode())) {
            throw new BizException(out.getMsg());
        }
        if ("8008".equals(out.getCode())) {
            return InvoiceReversal.builder()
                    .ticketCode(out.getTicketCode())
                    .ticketNo(out.getTicketNo())
                    .providerKey(key)
                    .checkCode(out.getCheckCode())
                    .url(out.getTicketUrl())
                    .build();
        }
        out = getByKey(reversal.bizCode);
        return InvoiceReversal.builder()
                .ticketCode(out.getTicketCode())
                .ticketNo(out.getTicketNo())
                .providerKey(key)
                .checkCode(out.getCheckCode())
                .url(out.getTicketUrl())
                .build();
    }
}