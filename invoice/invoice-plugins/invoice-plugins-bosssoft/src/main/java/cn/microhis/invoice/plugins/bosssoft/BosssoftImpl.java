package cn.microhis.invoice.plugins.bosssoft;


import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.model.enums.BizType;
import cn.microhis.invoice.plugins.bosssoft.OutpatientIn.PayChannel;
import cn.microhis.invoice.provider.PluginContext;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceHttpException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrUtil;

import java.util.List;

import static cn.com.idmy.base.util.Assert.notBlank;

@Slf4j
public class BosssoftImpl extends ProviderPluginBase<BosssoftConfig> implements ProviderPlugin {
    public BosssoftImpl() {
        super("BOSSSOFT");
    }

    @Override
    protected BosssoftConfig parseConfig(long tenantId, JSONObject config) {
        var cfg = new BosssoftConfig();
        cfg.appId = notBlank(config.getString("appId"), "没有配置发票供应商appId");
        cfg.secret = notBlank(config.getString("secret"), "没有配置发票供应商secret");
        cfg.url = notBlank(config.getString("url"), "没有配置发票供应商url");
        cfg.placeCode = config.getString("placeCode");
        return cfg;
    }

    @Override
    public InvoiceReversal doReverse(Invoice in) {
        var cfg = config();
        var ps = new ReversalIn();
        ps.placeCode = StrUtil.isBlank(cfg.placeCode) ? in.getPayeeCode() : cfg.placeCode;
        ps.billBatchCode = in.getTicketCode();
        ps.billNo = in.getTicketNo();
        ps.reason = in.getRemark();
        ps.operator = in.getPayee();
        ps.busDateTime = in.getCreatedAt();
        try {
            var out = Http.post(cfg, ps, "/api/medical/writeOffEBill", ReversalOut.class);
            return InvoiceReversal.builder()
                    .ticketCode(out.getEScarletBillBatchCode())
                    .ticketNo(out.getEScarletBillNo())
                    .ticketAt($.toLocalDateTime(out.getCreateTime()))
                    .checkCode(out.getEScarletRandom())
                    .url(out.getPictureUrl())
                    .build();
        } catch (InvoiceHttpException e) {
            if (e.getMessage().contains("票据已冲销")) {
                return null;
            } else {
                throw e;
            }
        }
    }

    @Override
    protected Invoice createReg(Invoice invoice) {
        return createOutpatient(invoice);
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        var cfg = config();
        var ps = new OutpatientIn();
        ps.placeCode = StrUtil.isBlank(cfg.placeCode) ? invoice.getPayeeCode() : cfg.placeCode;
        set(invoice, ps);
        var path = invoice.getBizType() == BizType.REG ? "/api/medical/invEBillRegistration" : "/api/medical/invoiceEBillOutpatient";
        post(cfg, path, ps, invoice);
        return invoice;
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        var cfg = config();
        var ps = new InpatientIn();
        ps.placeCode = StrUtil.isBlank(cfg.placeCode) ? invoice.getPayeeCode() : cfg.placeCode;
        set(invoice, ps);

        var inpatient = invoice.getInpatient();
        ps.hospitalNo = inpatient.getNo();
        ps.inHospitalDate = inpatient.getInAt();
        ps.outHospitalDate = inpatient.getOutAt();
        ps.hospitalDays = inpatient.getDay();
        ps.leaveCategoryCode = inpatient.getOutDeptCode();
        ps.leaveCategory = inpatient.getOutDept();

        post(cfg, "/api/medical/invEBillHospitalized", ps, invoice);
        return invoice;
    }

    private void set(Invoice inv, OutpatientIn main) {
        main.payerType = String.valueOf(inv.getPayerType().value());
        main.cardType = $.toCardType(inv.getCardType());
        main.patientId = String.valueOf(inv.getPayeeId());
        main.payer = inv.getName();
        main.payee = inv.getPayee();
        main.cardNo = inv.getCardNo();
        main.idCardNo = inv.getCardNo();
        main.age = inv.getAge();
        main.sex = inv.getGender();
        main.email = inv.getEmail();
        main.author = inv.getPayee();
        main.remark = inv.getRemark();
        main.category = inv.getDept();
        main.patientCategoryCode = inv.getDeptCode();
        main.busNo = inv.getProviderKey();
        main.busType = $.toBizType(inv.getBizType());
        main.eBillRelateNo = String.valueOf(inv.getBizId());
        main.busDateTime = inv.getCreatedAt();
        main.medicalInstitution = PluginContext.getMedicalInstitutionType().title();
        main.totalAmt = fmtAmt(inv.getAmt());
        main.ownPay = fmtAmt(inv.getOwnPay());
        main.selfCashPay = fmtAmt(inv.getCashPay());
        main.caseNumber = inv.getCaseNo();

        var outpatient = inv.getOutpatient();
        if (outpatient != null) {
            main.patientNo = outpatient.getVisitNo();
            main.consultationDate = outpatient.getVisitAt();
        }

        var mi = inv.getMedicalInsurance();
        if (mi != null) {
            main.medicalInsuranceID = mi.getNo();
            main.medicalCareType = mi.getType().title();
            main.medCareTypeCode = String.valueOf(mi.getType().value());
            main.fundPay = fmtAmt(mi.getFundPay());
            main.accountPay = fmtAmt(mi.getAccountPay());
            main.ownAcBalance = fmtAmt(mi.getAccountBalance());
            main.selfPayAmt = fmtAmt(mi.getSelfPay());
        }

        var details = $.toDetailsForDecimalPointProblem(mapFeeType(), inv);
        main.listDetail = $.toListDetails(details);
        main.chargeDetail = $.toChargeDetails(details);
        main.payChannelDetail = List.of(PayChannel.of("02", fmtAmt(inv.getAmt())));
    }

    private void post(BosssoftConfig cfg, String path, Object ps, Invoice invoice) {
        var out = Http.post(cfg, ps, path, BaseOut.class);
        invoice.setTicketCode(out.getBillBatchCode());
        invoice.setTicketNo(out.getBillNo());
        invoice.setCheckCode(out.getRandom());
        invoice.setUrl(out.getPictureUrl());
    }
}
