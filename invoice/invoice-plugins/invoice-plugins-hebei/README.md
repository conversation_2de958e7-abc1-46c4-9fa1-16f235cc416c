# 河北政府发票插件

## 概述

河北政府发票插件是基于河北省财政非税电子票据系统单位接口规范（医疗）开发的数据模型。

## 数据模型

根据PDF规范文档，创建了以下数据模型：

### 1. HebeiConfig - 配置类
包含河北政府发票服务所需的配置信息：
- `url` - 服务器URL地址
- `appId` - 应用ID
- `appSecret` - 应用密钥
- `orgCode` - 机构代码
- `orgName` - 机构名称
- `sellerTaxNo` - 销售方纳税人识别号
- `sellerName` - 销售方名称
- `sellerPhone` - 销售方电话（可选）
- `sellerAddress` - 销售方地址（可选）
- `sellerBank` - 销售方开户行（可选）
- `sellerBankAccount` - 销售方银行账号（可选）
- `testMode` - 是否为测试环境

### 2. HebeiInvoiceRequest - 发票请求主体
包含43个字段，对应PDF规范中的所有参数：

#### 基础信息
- `einvoiceSpecimen` - 电子票据模板（1:门诊 2:住院）
- `issueDate` - 开票日期（yyyyMMdd）
- `issueTime` - 开票时间（hh:mm:ss）
- `payerPartyType` - 交款人类型（1:个人 2:单位）
- `payerPartyCode` - 交款人代码
- `payerPartyName` - 交款人名称
- `totalAmount` - 总金额
- `bizCode` - 业务流水号

#### 医疗信息
- `medicalType` - 医疗类别
- `patientNumber` - 门诊号
- `medicalDate` - 就诊日期
- `medicalInsuranceType` - 医保类型
- `medicalInsuranceId` - 医保编号

#### 支付信息
- `fundPayAmount` - 医保统筹基金支付
- `otherPayAmount` - 其他支付
- `accountPayAmount` - 个人账户支付
- `ownPayAmount` - 个人现金支付
- `selfPaymentAmount` - 个人自付
- `selfPaymentCost` - 个人自费

#### 住院特有信息
- `caseNumber` - 病例号
- `hospitalizationNumber` - 住院号
- `departmentName` - 科别
- `inHospitalDate` - 住院日期
- `outHospitalDate` - 出院日期
- `prepayAmount` - 预缴金额
- `rechargeAmount` - 补缴金额
- `refundAmount` - 退费金额

### 3. HebeiInvoiceDetail - 项目信息
对应PDF规范中的details项目信息（List）：
- `itemCode` - 项目编码（必填）
- `itemName` - 项目名称（必填）
- `itemQuantity` - 数量（可选）
- `itemUnit` - 单位（可选）
- `itemStd` - 标准（可选）
- `itemAmount` - 金额（必填）
- `itemRemark` - 项目备注（可选）

### 4. HebeiInvoiceAuxDetail - 项目清单
对应PDF规范中的auxdetails项目清单（List）：
- `auxItemRelatedCode` - 对应项目编码（必填）
- `auxItemRelatedName` - 对应项目名称（必填）
- `auxItemCode` - 收费明细项目编码（可选）
- `auxItemName` - 收费明细项目名称（必填）
- `auxItemQuantity` - 收费明细项目数量（可选）
- `auxItemUnit` - 收费明细项目单位（可选）
- `auxItemStd` - 收费明细项目标准（可选）
- `auxItemAmount` - 收费明细项目金额（必填）
- `auxItemRemark` - 收费明细项目备注（可选）

### 5. HebeiInvoiceResponse - 发票响应
用于接收河北政府发票服务的响应结果：
- `success` - 是否成功
- `code` - 响应代码
- `message` - 响应消息
- `ticketCode` - 票据代码
- `ticketNo` - 票据号码
- `checkCode` - 校验码
- `invoiceUrl` - 发票URL
- `createTime` - 创建时间

## 注意事项

1. **仅包含数据模型**：当前只创建了数据模型，具体的业务逻辑实现需要根据河北政府提供的实际接口文档进行开发。

2. **字段映射**：所有字段都严格按照PDF规范中的字段名称和类型定义，使用`@JSONField`注解确保JSON序列化时的字段名称正确。

3. **数据类型**：
   - 金额字段使用`BigDecimal`类型
   - 日期时间字段使用`LocalDateTime`类型
   - 字符串字段使用`String`类型
   - 数值字段使用`BigDecimal`类型

4. **必填/可选**：根据PDF规范中的"强制/可选"列标注了字段的必填性。

5. **冲红功能**：PDF文档中没有提供冲红相关的接口规范，因此未创建冲红相关的数据模型。

## 使用说明

这些数据模型可以直接用于：
1. 构建发送给河北政府发票服务的请求参数
2. 解析河北政府发票服务返回的响应数据
3. 作为后续业务逻辑实现的基础

具体的HTTP通信、签名验证、加密解密等功能需要根据河北政府提供的技术文档进行实现。
