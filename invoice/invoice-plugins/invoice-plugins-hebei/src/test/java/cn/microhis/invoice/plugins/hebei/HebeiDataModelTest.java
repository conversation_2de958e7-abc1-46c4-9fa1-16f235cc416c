package cn.microhis.invoice.plugins.hebei;

import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 河北政府发票数据模型测试
 * 展示如何使用数据模型构建请求参数
 */
public class HebeiDataModelTest {

    @Test
    public void testBuildOutpatientRequest() {
        // 构建门诊发票请求数据模型
        var request = HebeiInvoiceIn.builder()
                .einvoiceSpecimen("1") // 1:门诊
                .issueDate(LocalDateTime.now())
                .issueTime(LocalDateTime.now())
                .payerPartyType("1") // 1:个人
                .payerPartyCode("130102199001011234")
                .payerPartyName("张三")
                .payerPhoneNumber("***********")
                .recName("收款员")
                .totalAmount(new BigDecimal("150.00"))
                .bizCode("BIZ***********")
                .handlingPerson("开票员")
                .checker("复核员")
                .remark("门诊挂号费")
                .businessNumber("MZ***********")
                .businessDate(LocalDateTime.now())
                .gender("男")
                .medicalType("门诊")
                .patientNumber("MZ***********")
                .medicalDate(LocalDateTime.now())
                .orgType("医院")
                .fundPayAmount(new BigDecimal("100.00"))
                .accountPayAmount(new BigDecimal("30.00"))
                .ownPayAmount(new BigDecimal("20.00"))
                .selfPaymentCost(new BigDecimal("0.00"))
                .build();

        // 构建项目信息
        var detail1 = HebeiInvoiceDetail.builder()
                .itemCode("001")
                .itemName("挂号费")
                .itemAmount(new BigDecimal("10.00"))
                .itemQuantity(new BigDecimal("1"))
                .itemUnit("次")
                .itemStd(new BigDecimal("10.00"))
                .build();

        var detail2 = HebeiInvoiceDetail.builder()
                .itemCode("002")
                .itemName("诊查费")
                .itemAmount(new BigDecimal("140.00"))
                .itemQuantity(new BigDecimal("1"))
                .itemUnit("次")
                .itemStd(new BigDecimal("140.00"))
                .build();

        request.setDetails(Arrays.asList(detail1, detail2));

        // 构建项目清单
        var auxDetail1 = HebeiInvoiceAuxDetail.builder()
                .auxItemRelatedCode("001")
                .auxItemRelatedName("挂号费")
                .auxItemCode("001001")
                .auxItemName("普通门诊挂号费")
                .auxItemQuantity(new BigDecimal("1"))
                .auxItemUnit("次")
                .auxItemStd(new BigDecimal("10.00"))
                .auxItemAmount(new BigDecimal("10.00"))
                .build();

        var auxDetail2 = HebeiInvoiceAuxDetail.builder()
                .auxItemRelatedCode("002")
                .auxItemRelatedName("诊查费")
                .auxItemCode("002001")
                .auxItemName("主任医师诊查费")
                .auxItemQuantity(new BigDecimal("1"))
                .auxItemUnit("次")
                .auxItemStd(new BigDecimal("140.00"))
                .auxItemAmount(new BigDecimal("140.00"))
                .build();

        request.setAuxDetails(Arrays.asList(auxDetail1, auxDetail2));

        // 输出JSON格式的请求数据
        String jsonRequest = JSON.toJSONString(request, true);
        System.out.println("门诊发票请求数据模型:");
        System.out.println(jsonRequest);
    }

    @Test
    public void testBuildInpatientRequest() {
        // 构建住院发票请求数据模型
        var request = HebeiInvoiceIn.builder()
                .einvoiceSpecimen("2") // 2:住院
                .issueDate(LocalDateTime.now())
                .issueTime(LocalDateTime.now())
                .payerPartyType("1") // 1:个人
                .payerPartyCode("130102199001011234")
                .payerPartyName("李四")
                .payerPhoneNumber("***********")
                .recName("收款员")
                .totalAmount(new BigDecimal("5000.00"))
                .bizCode("BIZ20231201002")
                .handlingPerson("开票员")
                .checker("复核员")
                .remark("住院医疗费")
                .businessNumber("ZY***********")
                .businessDate(LocalDateTime.now().minusDays(7))
                .gender("女")
                .medicalType("住院")
                .medicalDate(LocalDateTime.now().minusDays(7))
                .orgType("医院")
                .caseNumber("BL***********")
                .hospitalizationNumber("ZY***********")
                .departmentName("内科")
                .inHospitalDate(LocalDateTime.now().minusDays(7))
                .outHospitalDate(LocalDateTime.now())
                .prepayAmount(new BigDecimal("3000.00"))
                .rechargeAmount(new BigDecimal("2000.00"))
                .refundAmount(new BigDecimal("0.00"))
                .fundPayAmount(new BigDecimal("4000.00"))
                .accountPayAmount(new BigDecimal("500.00"))
                .ownPayAmount(new BigDecimal("500.00"))
                .selfPaymentCost(new BigDecimal("0.00"))
                .build();

        // 输出JSON格式的请求数据
        String jsonRequest = JSON.toJSONString(request, true);
        System.out.println("住院发票请求数据模型:");
        System.out.println(jsonRequest);
    }

    @Test
    public void testParseResponse() {
        // 模拟河北政府发票服务响应
        var response = HebeiInvoiceResponse.builder()
                .success(true)
                .code("200")
                .message("发票开具成功")
                .ticketCode("********")
                .ticketNo("***********")
                .checkCode("123456")
                .invoiceUrl("https://invoice.hebei.gov.cn/view?id=123456")
                .createTime(LocalDateTime.now())
                .bizCode("BIZ***********")
                .totalAmount("150.00")
                .payerName("张三")
                .invoiceStatus("已开具")
                .build();

        // 输出JSON格式的响应数据
        String jsonResponse = JSON.toJSONString(response, true);
        System.out.println("发票响应数据模型:");
        System.out.println(jsonResponse);

        // 测试响应判断方法
        System.out.println("是否成功: " + response.isSuccess());
        System.out.println("错误消息: " + response.getErrorMessage());
    }
}
