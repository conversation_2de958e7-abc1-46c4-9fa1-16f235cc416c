package cn.microhis.invoice.plugins.hebei;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 河北政府发票项目信息数据模型
 * 对应PDF规范中的details项目信息（List）
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "河北政府发票项目信息")
public class Detail {

    @JSONField(name = "itemcode")
    @Schema(title = "项目编码", description = "必填")
    String itemCode;

    @JSONField(name = "itemname")
    @Schema(title = "项目名称", description = "必填")
    String itemName;

    @JSONField(name = "itemquantity")
    @Schema(title = "数量", description = "可选")
    BigDecimal itemQuantity;

    @JSONField(name = "itemunit")
    @Schema(title = "单位", description = "可选")
    String itemUnit;

    @JSONField(name = "itemstd")
    @Schema(title = "标准", description = "可选")
    BigDecimal itemStd;

    @JSONField(name = "itemamount")
    @Schema(title = "金额", description = "必填")
    BigDecimal itemAmount;

    @JSONField(name = "itemremark")
    @Schema(title = "项目备注", description = "可选")
    String itemRemark;
}
