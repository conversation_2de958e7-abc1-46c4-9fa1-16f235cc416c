package cn.microhis.invoice.plugins.hebei;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 河北政府发票响应数据模型
 * 用于接收河北政府发票服务的响应结果
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "河北政府发票响应")
public class HebeiInvoiceResponse {

    @JSONField(name = "success")
    @Schema(title = "是否成功")
    private Boolean success;

    @JSONField(name = "code")
    @Schema(title = "响应代码")
    private String code;

    @JSONField(name = "message")
    @Schema(title = "响应消息")
    private String message;

    @JSONField(name = "error_message")
    @Schema(title = "错误消息")
    private String errorMessage;

    @JSONField(name = "ticket_code")
    @Schema(title = "票据代码")
    private String ticketCode;

    @JSONField(name = "ticket_no")
    @Schema(title = "票据号码")
    private String ticketNo;

    @JSONField(name = "check_code")
    @Schema(title = "校验码")
    private String checkCode;

    @JSONField(name = "invoice_url")
    @Schema(title = "发票URL")
    private String invoiceUrl;

    @JSONField(name = "create_time")
    @Schema(title = "创建时间")
    private LocalDateTime createTime;

    @JSONField(name = "biz_code")
    @Schema(title = "业务流水号")
    private String bizCode;

    @JSONField(name = "total_amount")
    @Schema(title = "总金额")
    private String totalAmount;

    @JSONField(name = "payer_name")
    @Schema(title = "交款人名称")
    private String payerName;

    @JSONField(name = "invoice_status")
    @Schema(title = "发票状态")
    private String invoiceStatus;

    /**
     * 判断响应是否成功
     *
     * @return 成功返回true，失败返回false
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success) || "200".equals(code) || "0000".equals(code);
    }

    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    public String getErrorMessage() {
        if (errorMessage != null) {
            return errorMessage;
        }
        if (message != null && !isSuccess()) {
            return message;
        }
        return "未知错误";
    }
}
