package cn.microhis.invoice.plugins.hebei;

import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.model.MedicalInsurance;
import cn.microhis.invoice.provider.PluginContext;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;

import static cn.com.idmy.base.util.Assert.notBlank;

@Slf4j
public class HebeiImpl extends ProviderPluginBase<HebeiConfig> implements ProviderPlugin {
    public HebeiImpl() {
        super("HEBEI");
    }

    @Override
    protected HebeiConfig parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        var cfg = new HebeiConfig();
        cfg.url = notBlank(config.getString("url"), "请配置服务器URL地址");
        cfg.agencySign = config.getString("agencySign");
        return cfg;
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        var in = buildIn(invoice);
        in.einvoiceSpecimen = "1";
        in.medicalType = "门诊";

        var o = invoice.getOutpatient();
        in.patientNumber = o.getNo();
        in.medicalDate = o.getVisitAt();
        var out = Http.post(config(), in, "/mkomedicaleinvoice", Out.class);

        return invoice;
    }

    @Override
    protected Invoice createInpatient(Invoice inv) {
        var in = buildIn(inv);
        in.einvoiceSpecimen = "2";
        in.medicalType = "住院";

        var i = inv.getInpatient();
        in.hospitalizationNumber = i.getNo();
        in.inHospitalDate = i.getInAt();
        in.outHospitalDate = i.getOutAt();
        in.prepayAmount = i.getPrepayAmt();
        in.rechargeAmount = i.getRechargeAmt();
        in.refundAmount = i.getRefundAmt();
        return inv;
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice invoice) {
        throw new UnsupportedOperationException("发票冲红功能暂未实现");
    }

    private In buildIn(Invoice inv) {
        var now = LocalDateTime.now();
        var in = new In();

        in.agencysign = config().agencySign;
        in.state = 1;

        in.payerPartyType = inv.getPayerType().value().toString();
        in.payerPartyCode = inv.getCardNo();
        in.payerPartyName = inv.getName();
        in.payerPhoneNumber = inv.getMobile();
        in.recName = inv.getPayee();
        in.gender = inv.getGender();
        in.bizCode = inv.getProviderKey();
        in.handlingPerson = inv.getPayee();
        in.checker = inv.getPayee();
        in.remark = inv.getRemark();

        in.totalAmount = inv.getAmt();
        in.otherPayAmount = inv.getOtherPay() == null ? BigDecimal.ZERO : inv.getOtherPay();
        in.ownPayAmount = inv.getCashPay() == null ? BigDecimal.ZERO : inv.getCashPay();
        in.selfPaymentCost = inv.getOwnPay() == null ? BigDecimal.ZERO : inv.getOwnPay();

        in.departmentName = inv.getDept();
        in.issueDate = now;
        in.issueTime = now;
        in.businessNumber = inv.getBizId().toString();
        in.businessDate = now;
        in.orgType = PluginContext.getMedicalInstitutionType().title();
        in.caseNumber = inv.getCaseNo();

        setMedicalInsuranceInfo(in, inv.getMedicalInsurance());
        buildDetails(in, inv);
        return in;
    }

    private static void setMedicalInsuranceInfo(In in, MedicalInsurance mi) {
        if (mi == null) {
            in.fundPayAmount = BigDecimal.ZERO;
            in.accountPayAmount = BigDecimal.ZERO;
            in.selfPaymentAmount = BigDecimal.ZERO;
        } else {
            in.medicalInsuranceType = mi.getType() != null ? mi.getType().title() : null;
            in.medicalInsuranceId = mi.getNo();
            in.fundPayAmount = mi.getFundPay() != null ? mi.getFundPay() : BigDecimal.ZERO;
            in.accountPayAmount = mi.getAccountPay() != null ? mi.getAccountPay() : BigDecimal.ZERO;
            in.selfPaymentAmount = mi.getSelfPay() != null ? mi.getSelfPay() : BigDecimal.ZERO;
        }
    }

    private static void buildDetails(In in, Invoice inv) {
        var auxDetails = new ArrayList<AuxDetail>();

        // 按费用类型分组构建项目信息
        var feeTypeMap = new HashMap<String, Detail>();
        var totalAmt = BigDecimal.ZERO;

        var details = inv.getDetails();
        for (int i = 0, size = details.size(); i < size; i++) {
            var detail = details.get(i);
            var feeTypeCode = detail.getFeeTypeCode();
            var feeTypeName = detail.getFeeType();

            var projectDetail = feeTypeMap.get(feeTypeCode);
            if (projectDetail == null) {
                projectDetail = new Detail();
                projectDetail.itemCode = feeTypeCode;
                projectDetail.itemName = feeTypeName;
                projectDetail.itemAmount = detail.getAmt();
                projectDetail.itemQuantity = detail.getQty();
                projectDetail.itemUnit = detail.getUnit();
                projectDetail.itemStd = detail.getPrice();
                feeTypeMap.put(feeTypeCode, projectDetail);
            } else {
                // 累加同类型费用的金额和数量
                projectDetail.itemAmount = projectDetail.itemAmount.add(detail.getAmt());
                if (projectDetail.itemQuantity != null && detail.getQty() != null) {
                    projectDetail.itemQuantity = projectDetail.itemQuantity.add(detail.getQty());
                }
            }

            // 构建项目清单（每个收费明细对应一条记录）
            var auxDetail = new AuxDetail();
            auxDetail.auxItemRelatedCode = feeTypeCode;
            auxDetail.auxItemRelatedName = feeTypeName;
            auxDetail.auxItemCode = detail.getCode();
            auxDetail.auxItemName = detail.getName();
            auxDetail.auxItemQuantity = detail.getQty();
            auxDetail.auxItemUnit = detail.getUnit();
            auxDetail.auxItemStd = detail.getPrice();
            auxDetail.auxItemAmount = detail.getAmt();
            auxDetails.add(auxDetail);
            totalAmt = totalAmt.add(detail.getAmt());
        }

        in.details = new ArrayList<>(feeTypeMap.values());
        in.auxDetails = auxDetails;
    }
}
