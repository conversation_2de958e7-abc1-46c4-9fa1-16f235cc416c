package cn.microhis.invoice.plugins.hebei;

import cn.microhis.invoice.model.Inpatient;
import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.model.MedicalInsurance;
import cn.microhis.invoice.provider.PluginContext;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;

import static cn.com.idmy.base.util.Assert.notBlank;

@Slf4j
public class HebeiImpl extends ProviderPluginBase<HebeiConfig> implements ProviderPlugin {
    public HebeiImpl() {
        super("HEBEI");
    }

    @Override
    protected HebeiConfig parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        var cfg = new HebeiConfig();

        // 必填配置项验证
        cfg.setUrl(notBlank(config.getString("url"), "请配置服务器URL地址"));
        cfg.setAppId(notBlank(config.getString("appId"), "请配置应用ID"));
        cfg.setAppSecret(notBlank(config.getString("appSecret"), "请配置应用密钥"));
        return cfg;
    }

    @Override
    protected Invoice createRegistration(Invoice invoice) {
        return createOutpatient(invoice);
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        var in = buildRequest(invoice);
        var outpatient = invoice.getOutpatient();
        in.einvoiceSpecimen = "1";
        in.medicalType = "门诊";
        in.patientNumber = outpatient.getNo();
        in.medicalDate = outpatient.getVisitAt();
        return invoice;
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        var in = buildRequest(invoice);
        var inpatient = invoice.getInpatient();
        in.einvoiceSpecimen = "2";
        in.medicalType = "住院";
        in.caseNumber = inpatient.getMedicalRecordNo();
        in.hospitalizationNumber = inpatient.getNo();
        in.departmentName = invoice.getDept();
        in.inHospitalDate = inpatient.getInAt();
        in.outHospitalDate = inpatient.getOutAt();
        in.prepayAmount = inpatient.getPrepayAmt();
        in.rechargeAmount = inpatient.getRechargeAmt();
        in.refundAmount = inpatient.getRefundAmt();
        return invoice;
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice invoice) {
        // TODO: 实现发票冲红逻辑（需要河北政府提供冲红接口文档）
        throw new UnsupportedOperationException("发票冲红功能暂未实现");
    }

    private HebeiInvoiceIn buildRequest(Invoice invoice) {
        var now = LocalDateTime.now();

        var in = HebeiInvoiceIn.builder()
                .issueDate(now)
                .issueTime(now)
                .payerPartyType(invoice.getPayerType().value().toString()) // 1:个人 2:单位
                .payerPartyCode(invoice.getCardNo())
                .payerPartyName(invoice.getName())
                .payerPhoneNumber(invoice.getMobile())
                .recName(invoice.getPayee())
                .totalAmount(invoice.getAmt())
                .bizCode(invoice.getProviderKey())
                .handlingPerson(invoice.getPayee())
                .checker(invoice.getPayee())
                .remark(invoice.getRemark())
                .businessNumber(invoice.getBizId().toString())
                .businessDate(now)
                .gender(invoice.getGender())
                .orgType(PluginContext.getMedicalInstitutionType().title())
                .build();

        setMedicalInsuranceInfo(in, invoice.getMedicalInsurance());
        setPaymentInfo(in, invoice);
        buildDetails(in, invoice);
        return in;
    }

    /**
     * 设置医保信息
     */
    private void setMedicalInsuranceInfo(HebeiInvoiceIn request, MedicalInsurance mi) {
        if (mi == null) {
            request.setFundPayAmount(BigDecimal.ZERO);
            request.setAccountPayAmount(BigDecimal.ZERO);
            request.setSelfPaymentAmount(BigDecimal.ZERO);
        } else {
            request.setMedicalInsuranceType(mi.getType() != null ? mi.getType().title() : null);
            request.setMedicalInsuranceId(mi.getNo());
            request.setFundPayAmount(mi.getFundPay() != null ? mi.getFundPay() : BigDecimal.ZERO);
            request.setAccountPayAmount(mi.getAccountPay() != null ? mi.getAccountPay() : BigDecimal.ZERO);
            request.setSelfPaymentAmount(mi.getSelfPay() != null ? mi.getSelfPay() : BigDecimal.ZERO);
        }
    }

    /**
     * 设置支付信息
     */
    private void setPaymentInfo(HebeiInvoiceIn request, Invoice invoice) {
        request.setOtherPayAmount(invoice.getOtherPay() == null ? BigDecimal.ZERO : invoice.getOtherPay());
        request.setOwnPayAmount(invoice.getCashPay() == null ? BigDecimal.ZERO : invoice.getCashPay());
        request.setSelfPaymentCost(invoice.getOwnPay() == null ? BigDecimal.ZERO : invoice.getOwnPay());
    }

    /**
     * 构建发票项目信息和项目清单
     * 将发票系统的详情数据转换为河北供应商需要的格式
     */
    private void buildDetails(HebeiInvoiceIn request, Invoice invoice) {
        var auxDetails = new java.util.ArrayList<HebeiInvoiceAuxDetail>();

        // 按费用类型分组构建项目信息
        var feeTypeMap = new java.util.HashMap<String, HebeiInvoiceDetail>();
        var totalAmt = BigDecimal.ZERO;

        for (var detail : invoice.getDetails()) {
            var feeTypeCode = detail.getFeeTypeCode();
            var feeTypeName = detail.getFeeType();

            // 构建或更新项目信息（按费用类型分组）
            var projectDetail = feeTypeMap.get(feeTypeCode);
            if (projectDetail == null) {
                projectDetail = HebeiInvoiceDetail.builder()
                        .itemCode(feeTypeCode)
                        .itemName(feeTypeName)
                        .itemAmount(detail.getAmt())
                        .itemQuantity(detail.getQty())
                        .itemUnit(detail.getUnit())
                        .itemStd(detail.getPrice())
                        .build();
                feeTypeMap.put(feeTypeCode, projectDetail);
            } else {
                // 累加同类型费用的金额和数量
                projectDetail.setItemAmount(projectDetail.getItemAmount().add(detail.getAmt()));
                if (projectDetail.getItemQuantity() != null && detail.getQty() != null) {
                    projectDetail.setItemQuantity(projectDetail.getItemQuantity().add(detail.getQty()));
                }
            }

            // 构建项目清单（每个收费明细对应一条记录）
            var auxDetail = HebeiInvoiceAuxDetail.builder()
                    .auxItemRelatedCode(feeTypeCode)
                    .auxItemRelatedName(feeTypeName)
                    .auxItemCode(detail.getCode())
                    .auxItemName(detail.getName())
                    .auxItemQuantity(detail.getQty())
                    .auxItemUnit(detail.getUnit())
                    .auxItemStd(detail.getPrice())
                    .auxItemAmount(detail.getAmt())
                    .build();
            auxDetails.add(auxDetail);
            totalAmt = totalAmt.add(detail.getAmt());
        }

        // 设置到请求对象中
        var details = new ArrayList<>(feeTypeMap.values());
        request.setDetails(details);
        request.setAuxDetails(auxDetails);

        log.info("构建发票详情完成，项目数: {}, 明细数: {}, 总金额: {}", details.size(), auxDetails.size(), totalAmt);
    }
}
