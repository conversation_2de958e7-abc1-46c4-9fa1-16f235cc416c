package cn.microhis.invoice.plugins.hebei;

import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import static cn.com.idmy.base.util.Assert.notBlank;

@Slf4j
public class HebeiImpl extends ProviderPluginBase<HebeiConfig> implements ProviderPlugin {
    public HebeiImpl() {
        super("HEBEI");
    }

    @Override
    protected HebeiConfig parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        var cfg = new HebeiConfig();
        cfg.setUrl(notBlank(config.getString("url"), "请配置服务器URL地址"));
        cfg.setAppId(notBlank(config.getString("appId"), "请配置应用ID"));
        cfg.setAppSecret(notBlank(config.getString("appSecret"), "请配置应用密钥"));
        return cfg;
    }

    @Override
    protected Invoice createRegistration(Invoice invoice) {
        // TODO: 实现挂号发票创建逻辑
        throw new UnsupportedOperationException("挂号发票创建功能暂未实现");
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        log.info("开始创建门诊发票，业务ID: {}", invoice.getBizId());

        // 将发票系统数据转换为河北供应商数据模型
        HebeiInvoiceRequest request = buildOutpatientRequest(invoice);

        // TODO: 调用河北政府发票服务接口
        // HebeiInvoiceResponse response = callHebeiApi(request);

        log.info("门诊发票请求数据构建完成: {}", request);

        // TODO: 实现具体的HTTP调用和响应处理
        throw new UnsupportedOperationException("门诊发票HTTP调用功能暂未实现");
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        log.info("开始创建住院发票，业务ID: {}", invoice.getBizId());

        // 将发票系统数据转换为河北供应商数据模型
        HebeiInvoiceRequest request = buildInpatientRequest(invoice);

        // TODO: 调用河北政府发票服务接口
        // HebeiInvoiceResponse response = callHebeiApi(request);

        log.info("住院发票请求数据构建完成: {}", request);

        // TODO: 实现具体的HTTP调用和响应处理
        throw new UnsupportedOperationException("住院发票HTTP调用功能暂未实现");
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice invoice) {
        // TODO: 实现发票冲红逻辑（需要河北政府提供冲红接口文档）
        throw new UnsupportedOperationException("发票冲红功能暂未实现");
    }

    /**
     * 构建门诊发票请求数据模型
     * 将发票系统的Invoice对象转换为河北供应商需要的HebeiInvoiceRequest
     */
    private HebeiInvoiceRequest buildOutpatientRequest(Invoice invoice) {
        var cfg = config();
        var now = java.time.LocalDateTime.now();
        var outpatient = invoice.getOutpatient();
        var mi = invoice.getMedicalInsurance();

        // 构建基础请求数据
        var request = HebeiInvoiceRequest.builder()
                .einvoiceSpecimen("1") // 1:门诊
                .issueDate(now)
                .issueTime(now)
                .payerPartyType(invoice.getPayerType().value().toString()) // 1:个人 2:单位
                .payerPartyCode(invoice.getCardNo())
                .payerPartyName(invoice.getName())
                .payerPhoneNumber(invoice.getMobile())
                .recName(invoice.getPayee())
                .totalAmount(invoice.getAmt())
                .bizCode(invoice.getInvoiceId().toString())
                .handlingPerson(invoice.getPayee())
                .checker(invoice.getPayee())
                .remark(invoice.getRemark())
                .businessNumber(outpatient != null ? outpatient.getNo() : null)
                .businessDate(outpatient != null ? outpatient.getVisitAt() : now)
                .gender(invoice.getGender())
                .medicalType("门诊")
                .patientNumber(outpatient != null ? outpatient.getNo() : null)
                .medicalDate(outpatient != null ? outpatient.getVisitAt() : now)
                .orgType("医院")
                .build();

        // 设置医保信息
        setMedicalInsuranceInfo(request, mi);

        // 设置支付信息
        setPaymentInfo(request, invoice);

        // 构建项目信息和项目清单
        buildInvoiceDetails(request, invoice);

        return request;
    }

    /**
     * 构建住院发票请求数据模型
     * 将发票系统的Invoice对象转换为河北供应商需要的HebeiInvoiceRequest
     */
    private HebeiInvoiceRequest buildInpatientRequest(Invoice invoice) {
        var cfg = config();
        var now = java.time.LocalDateTime.now();
        var inpatient = invoice.getInpatient();
        var mi = invoice.getMedicalInsurance();

        // 构建基础请求数据
        var request = HebeiInvoiceRequest.builder()
                .einvoiceSpecimen("2") // 2:住院
                .issueDate(now)
                .issueTime(now)
                .payerPartyType(invoice.getPayerType().value().toString()) // 1:个人 2:单位
                .payerPartyCode(invoice.getCardNo())
                .payerPartyName(invoice.getName())
                .payerPhoneNumber(invoice.getMobile())
                .recName(invoice.getPayee())
                .totalAmount(invoice.getAmt())
                .bizCode(invoice.getInvoiceId().toString())
                .handlingPerson(invoice.getPayee())
                .checker(invoice.getPayee())
                .remark(invoice.getRemark())
                .businessNumber(inpatient != null ? inpatient.getNo() : null)
                .businessDate(inpatient != null ? inpatient.getInAt() : now)
                .gender(invoice.getGender())
                .medicalType("住院")
                .medicalDate(inpatient != null ? inpatient.getInAt() : now)
                .orgType("医院")
                .build();

        // 设置住院特有信息
        if (inpatient != null) {
            request.setCaseNumber(inpatient.getCaseNo());
            request.setHospitalizationNumber(inpatient.getNo());
            request.setDepartmentName(inpatient.getOutDept());
            request.setInHospitalDate(inpatient.getInAt());
            request.setOutHospitalDate(inpatient.getOutAt());
            // 预缴、补缴、退费金额根据实际业务需求设置
            request.setPrepayAmount(java.math.BigDecimal.ZERO);
            request.setRechargeAmount(java.math.BigDecimal.ZERO);
            request.setRefundAmount(java.math.BigDecimal.ZERO);
        }

        // 设置医保信息
        setMedicalInsuranceInfo(request, mi);

        // 设置支付信息
        setPaymentInfo(request, invoice);

        // 构建项目信息和项目清单
        buildInvoiceDetails(request, invoice);

        return request;
    }

    /**
     * 设置医保信息
     */
    private void setMedicalInsuranceInfo(HebeiInvoiceRequest request, cn.microhis.invoice.model.MedicalInsurance mi) {
        if (mi != null) {
            request.setMedicalInsuranceType(mi.getType() != null ? mi.getType().title() : null);
            request.setMedicalInsuranceId(mi.getNo());
            request.setFundPayAmount(mi.getFundPay() != null ? mi.getFundPay() : java.math.BigDecimal.ZERO);
            request.setAccountPayAmount(mi.getAccountPay() != null ? mi.getAccountPay() : java.math.BigDecimal.ZERO);
            request.setSelfPaymentAmount(mi.getSelfPay() != null ? mi.getSelfPay() : java.math.BigDecimal.ZERO);
        } else {
            request.setFundPayAmount(java.math.BigDecimal.ZERO);
            request.setAccountPayAmount(java.math.BigDecimal.ZERO);
            request.setSelfPaymentAmount(java.math.BigDecimal.ZERO);
        }
    }

    /**
     * 设置支付信息
     */
    private void setPaymentInfo(HebeiInvoiceRequest request, Invoice invoice) {
        request.setOtherPayAmount(invoice.getOtherPay() != null ? invoice.getOtherPay() : java.math.BigDecimal.ZERO);
        request.setOwnPayAmount(invoice.getCashPay() != null ? invoice.getCashPay() : java.math.BigDecimal.ZERO);
        request.setSelfPaymentCost(invoice.getOwnPay() != null ? invoice.getOwnPay() : java.math.BigDecimal.ZERO);
    }

    /**
     * 构建发票项目信息和项目清单
     * 将发票系统的详情数据转换为河北供应商需要的格式
     */
    private void buildInvoiceDetails(HebeiInvoiceRequest request, Invoice invoice) {
        var details = new java.util.ArrayList<HebeiInvoiceDetail>();
        var auxDetails = new java.util.ArrayList<HebeiInvoiceAuxDetail>();

        // 按费用类型分组构建项目信息
        var feeTypeMap = new java.util.HashMap<String, HebeiInvoiceDetail>();
        var totalAmt = java.math.BigDecimal.ZERO;

        for (var detail : invoice.getDetails()) {
            var feeTypeCode = detail.getFeeTypeCode();
            var feeTypeName = detail.getFeeType();

            // 构建或更新项目信息（按费用类型分组）
            var projectDetail = feeTypeMap.get(feeTypeCode);
            if (projectDetail == null) {
                projectDetail = HebeiInvoiceDetail.builder()
                        .itemCode(feeTypeCode)
                        .itemName(feeTypeName)
                        .itemAmount(detail.getAmt())
                        .itemQuantity(detail.getQty())
                        .itemUnit(detail.getUnit())
                        .itemStd(detail.getPrice())
                        .build();
                feeTypeMap.put(feeTypeCode, projectDetail);
            } else {
                // 累加同类型费用的金额和数量
                projectDetail.setItemAmount(projectDetail.getItemAmount().add(detail.getAmt()));
                if (projectDetail.getItemQuantity() != null && detail.getQty() != null) {
                    projectDetail.setItemQuantity(projectDetail.getItemQuantity().add(detail.getQty()));
                }
            }

            // 构建项目清单（每个收费明细对应一条记录）
            var auxDetail = HebeiInvoiceAuxDetail.builder()
                    .auxItemRelatedCode(feeTypeCode)
                    .auxItemRelatedName(feeTypeName)
                    .auxItemCode(detail.getCode())
                    .auxItemName(detail.getName())
                    .auxItemQuantity(detail.getQty())
                    .auxItemUnit(detail.getUnit())
                    .auxItemStd(detail.getPrice())
                    .auxItemAmount(detail.getAmt())
                    .build();
            auxDetails.add(auxDetail);

            totalAmt = totalAmt.add(detail.getAmt());
        }

        // 设置到请求对象中
        details.addAll(feeTypeMap.values());
        request.setDetails(details);
        request.setAuxDetails(auxDetails);

        log.info("构建发票详情完成，项目数: {}, 明细数: {}, 总金额: {}",
                details.size(), auxDetails.size(), totalAmt);
    }
}
