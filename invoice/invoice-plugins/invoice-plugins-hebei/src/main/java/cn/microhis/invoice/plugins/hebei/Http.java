package cn.microhis.invoice.plugins.hebei;


import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Setter
@RequiredArgsConstructor
class Http {
    static final RestTemplate restTemplate;

    static {
        var factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(3000);
        restTemplate = new RestTemplate(factory);
    }

    static <T> T post(HebeiConfig cfg, Object data, String path, Class<T> clazz) {
        log.info("电子发票：url:{}, input: {}", path, JSON.toJSONString(data));
        return null;
    }
}