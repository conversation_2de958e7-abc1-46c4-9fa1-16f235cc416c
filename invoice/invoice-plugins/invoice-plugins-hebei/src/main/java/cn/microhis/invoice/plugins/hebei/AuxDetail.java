package cn.microhis.invoice.plugins.hebei;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 河北政府发票项目清单数据模型
 * 对应PDF规范中的auxdetails项目清单（List）
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "河北政府发票项目清单")
public class AuxDetail {
    @JSONField(name = "auxitemrelatedcode")
    @Schema(title = "对应项目编码", description = "收费明细项目对应的项目编码，必填")
    String auxItemRelatedCode;

    @JSONField(name = "auxitemrelatedname")
    @Schema(title = "对应项目名称", description = "收费明细项目对应的项目名称，必填")
    String auxItemRelatedName;

    @JSONField(name = "auxitemcode")
    @Schema(title = "收费明细项目编码", description = "可选")
    String auxItemCode;

    @JSONField(name = "auxitemname")
    @Schema(title = "收费明细项目名称", description = "必填")
    String auxItemName;

    @JSONField(name = "auxitemquantity")
    @Schema(title = "收费明细项目数量", description = "可选")
    BigDecimal auxItemQuantity;

    @JSONField(name = "auxitemunit")
    @Schema(title = "收费明细项目单位", description = "可选")
    String auxItemUnit;

    @JSONField(name = "auxitemstd")
    @Schema(title = "收费明细项目标准", description = "可选")
    BigDecimal auxItemStd;

    @JSONField(name = "auxitemamount")
    @Schema(title = "收费明细项目金额", description = "必填")
    BigDecimal auxItemAmount;

    @JSONField(name = "auxitemremark")
    @Schema(title = "收费明细项目备注", description = "可选")
    String auxItemRemark;
}