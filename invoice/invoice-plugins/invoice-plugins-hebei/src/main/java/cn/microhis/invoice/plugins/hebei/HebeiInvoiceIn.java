package cn.microhis.invoice.plugins.hebei;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 河北政府发票请求主体数据模型
 * 根据河北省财政非税电子票据系统单位接口规范（医疗）
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "河北政府发票请求参数")
public class HebeiInvoiceIn {

    @JSONField(name = "einvoicespecimen")
    @Schema(title = "电子票据模板", description = "1:门诊 2:住院")
    private String einvoiceSpecimen;

    @JSONField(name = "issuedate", format = "yyyyMMdd")
    @Schema(title = "开票日期", description = "yyyyMMdd")
    private LocalDateTime issueDate;

    @JSONField(name = "issuetime", format = "HH:mm:ss")
    @Schema(title = "开票时间", description = "hh:mm:ss")
    private LocalDateTime issueTime;

    @JSONField(name = "payerpartytype")
    @Schema(title = "交款人类型", description = "1:个人 2:单位")
    private String payerPartyType;

    @JSONField(name = "payerpartycode")
    @Schema(title = "交款人代码", description = "单位一般为组织机构代码；个人一般为身份证号")
    private String payerPartyCode;

    @JSONField(name = "payerpartyname")
    @Schema(title = "交款人名称")
    private String payerPartyName;

    @JSONField(name = "payeracct")
    @Schema(title = "交款人账号")
    private String payerAcct;

    @JSONField(name = "payeropbk")
    @Schema(title = "交款人开户行")
    private String payerOpbk;

    @JSONField(name = "payerphonenumber")
    @Schema(title = "交款人手机号", description = "收票人手机号")
    private String payerPhoneNumber;

    @JSONField(name = "recname")
    @Schema(title = "收款人")
    private String recName;

    @JSONField(name = "recacct")
    @Schema(title = "收款人账号")
    private String recAcct;

    @JSONField(name = "recopbk")
    @Schema(title = "收款人开户行")
    private String recOpbk;

    @JSONField(name = "totalamount")
    @Schema(title = "总金额", description = "项目总金额")
    private BigDecimal totalAmount;

    @JSONField(name = "bizcode")
    @Schema(title = "业务流水号", description = "业务号必填，唯一")
    private String bizCode;

    @JSONField(name = "handlingperson")
    @Schema(title = "开票人（收款人）", description = "票据右下角收款人")
    private String handlingPerson;

    @JSONField(name = "checker")
    @Schema(title = "复核人", description = "票据右下角复核人")
    private String checker;

    @JSONField(name = "remark")
    @Schema(title = "其他信息", description = "备注中插入\\r\\n标识换行标记")
    private String remark;

    @JSONField(name = "relatedinvoicecode")
    @Schema(title = "相关票据代码", description = "预留扩展字段，开具红票时在此填写原票据代码")
    private String relatedInvoiceCode;

    @JSONField(name = "relatedinvoicenumber")
    @Schema(title = "相关票据号码", description = "预留扩展字段，开具红票时在此填写原票据号码")
    private String relatedInvoiceNumber;

    @JSONField(name = "businessnumber")
    @Schema(title = "业务单号", description = "门诊、住院填写")
    private String businessNumber;

    @JSONField(name = "businessdate", format = "yyyyMMdd")
    @Schema(title = "业务日期", description = "yyyymmdd (门诊、住院填写)")
    private LocalDateTime businessDate;

    @JSONField(name = "gender")
    @Schema(title = "性别", description = "男、女 (门诊、住院填写)")
    private String gender;

    @JSONField(name = "medicaltype")
    @Schema(title = "医疗类别", description = "门诊、急诊、住院等 (门诊、住院填写)")
    private String medicalType;

    @JSONField(name = "patientnumber")
    @Schema(title = "门诊号", description = "门诊填写")
    private String patientNumber;

    @JSONField(name = "medicaldate", format = "yyyyMMdd")
    @Schema(title = "就诊日期", description = "yyyymmdd (门诊、住院填写)")
    private LocalDateTime medicalDate;

    @JSONField(name = "orgtype")
    @Schema(title = "医疗机构类型", description = "门诊、住院填写")
    private String orgType;

    @JSONField(name = "medicalinsurancetype")
    @Schema(title = "医保类型", description = "门诊、住院填写")
    private String medicalInsuranceType;

    @JSONField(name = "medicalinsuranceid")
    @Schema(title = "医保编号", description = "门诊、住院填写")
    private String medicalInsuranceId;

    @JSONField(name = "fundpayamount")
    @Schema(title = "医保统筹基金支付", description = "门诊、住院填写")
    private BigDecimal fundPayAmount;

    @JSONField(name = "otherpayamount")
    @Schema(title = "其他支付", description = "门诊、住院填写")
    private BigDecimal otherPayAmount;

    @JSONField(name = "accountpayamount")
    @Schema(title = "个人账户支付", description = "门诊、住院填写")
    private BigDecimal accountPayAmount;

    @JSONField(name = "ownpayamount")
    @Schema(title = "个人现金支付", description = "门诊、住院填写")
    private BigDecimal ownPayAmount;

    @JSONField(name = "selfpaymentamount")
    @Schema(title = "个人自付", description = "门诊、住院填写")
    private BigDecimal selfPaymentAmount;

    @JSONField(name = "selfpaymentcost")
    @Schema(title = "个人自费", description = "门诊、住院填写")
    private BigDecimal selfPaymentCost;

    @JSONField(name = "casenumber")
    @Schema(title = "病例号", description = "住院填写")
    private String caseNumber;

    @JSONField(name = "hospitalizationnumber")
    @Schema(title = "住院号", description = "住院填写")
    private String hospitalizationNumber;

    @JSONField(name = "departmentname")
    @Schema(title = "科别", description = "住院填写")
    private String departmentName;

    @JSONField(name = "inhospitaldate", format = "yyyyMMdd")
    @Schema(title = "住院日期", description = "yyyymmdd (住院填写)")
    private LocalDateTime inHospitalDate;

    @JSONField(name = "outhospitaldate", format = "yyyyMMdd")
    @Schema(title = "出院日期", description = "yyyymmdd (住院填写)")
    private LocalDateTime outHospitalDate;

    @JSONField(name = "prepayamount")
    @Schema(title = "预缴金额", description = "住院填写")
    private BigDecimal prepayAmount;

    @JSONField(name = "rechargeamount")
    @Schema(title = "补缴金额", description = "住院填写")
    private BigDecimal rechargeAmount;

    @JSONField(name = "refundamount")
    @Schema(title = "退费金额", description = "住院填写")
    private BigDecimal refundAmount;

    @JSONField(name = "state")
    @Schema(title = "获取返回参数方式", description = "1：直接返回data，需等待 2：无data，后续发主动获取接口查询（空值，默认为2）")
    private Integer state;

    @JSONField(name = "details")
    @Schema(title = "项目信息")
    private List<HebeiInvoiceDetail> details;

    @JSONField(name = "auxdetails")
    @Schema(title = "项目清单")
    private List<HebeiInvoiceAuxDetail> auxDetails;
}
