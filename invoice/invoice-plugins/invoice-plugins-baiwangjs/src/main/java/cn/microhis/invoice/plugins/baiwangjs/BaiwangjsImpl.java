package cn.microhis.invoice.plugins.baiwangjs;

import cn.com.idmy.base.exception.BizException;
import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceHttpException;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.lang.tuple.Pair;
import org.dromara.hutool.core.math.NumberUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import static cn.com.idmy.base.util.Assert.notBlank;

@Slf4j
public class BaiwangjsImpl extends ProviderPluginBase<BaiwangjsConfig> implements ProviderPlugin {
    public BaiwangjsImpl() {
        super("BAIWANGJS");
    }

    @Override
    public BaiwangjsConfig parseConfig(long tenantId, JSONObject in) {
        var cfg = new BaiwangjsConfig();
        cfg.companyId = notBlank(in.getString("companyId"), "请配置companyId");
        cfg.sellerMobile = notBlank(in.getString("sellerMobile"), "请配置sellerMobile");
        cfg.sellerName = notBlank(in.getString("sellerName"), "请配置sellerName");
        cfg.sellerOrgCode = notBlank(in.getString("sellerOrgCode"), "请配置sellerOrgCode");
        cfg.sellerBankAccount = notBlank(in.getString("sellerBankAccount"), "请配置sellerBankAccount");
        cfg.sellerBankAddress = notBlank(in.getString("sellerBankAddress"), "请配置sellerBankAddress");
        cfg.url = notBlank(in.getString("url"), "请配置url");
        cfg.buildSign(notBlank(in.getString("appid"), "请配置appid"), notBlank(in.getString("secret"), "请配置secret"));
        return cfg;
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice in) {
        var cfg = config();
        var info = getInfo(in.getProviderKey());
        if (info == null) {
            return null;
        } else if (info.scarleted) {
            return null;
        }
        for (int i = 0; i < info.detailParam.size(); i++) {
            var detail = info.detailParam.get(i);
            detail.xh = String.valueOf(i + 1);
            detail.lzmxxh = detail.xh;
            detail.lslbs = "0";
            detail.je = "-" + detail.je;
            detail.fphxz = "0";
            detail.slv = "0";
            detail.hsbz = "1";
        }
        var main = Reversal.builder()
                .qyId(cfg.companyId)
                .yfpHm(info.qdfphm)
                .fplxdm(info.fplxdm)
                .xsfDh(info.xsfDh)
                .xsfMc(info.xsfMc)
                .xsfNsrsbh(info.xsfNsrsbh)
                .xsfYh(info.xsfYh)
                .xsfZh(info.xsfZh)
                .tspz(info.tspz)
                .zsfs(info.zsfs)
                .ykprq(DateUtil.format(DateUtil.parse(info.kprq), "yyyy-MM-dd HH:mm:ss"))
                .qdbz("0")
                .hjje("-" + info.hjje)
                .kplx("0")
                .kplx("0")
                .gmfzrrbs("Y")
                .sfwzzfp("N")
                .spxxbcsm("1")
                .chyydm("1")
                .dlzh($.getPayeeAccount(in.getPayeeId()))
                .yfplxdm(info.fplxdm)
                .ddlsh(in.getProviderKey())
                .gmfMc(info.gmfMc)
                .gmfMobile(info.gmfMobile)
                .gmfEmail(info.gmfEmail)
                .gmfNsrsbh(info.gmfNsrsbh)
                .kpr(info.kpr)
                .detailParam(info.detailParam)
                .build();
        Http.post(cfg, main, "/cloud/applyElecRedInvoice");
        return null;
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        var fundPay = buildFundPay(invoice);
        var mi = invoice.getMedicalInsurance();
        invoice.setRemark(StrUtil.format("科室：{}，医保类型：{}，医保统筹：{}，医保个账：{}，个人自费：{}",
                invoice.getDept(),
                mi == null || mi.getType() == null ? "无" : mi.getType().title(),
                fundPay.getLeft(),
                fmtAmt(mi == null ? 0 : mi.getAccountPay()),
                fmtAmt(invoice.getOwnPay())) + fundPay.getRight());
        return post(set(invoice));
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        var fundPay = buildFundPay(invoice);
        var inpatient = invoice.getInpatient();
        var mi = invoice.getMedicalInsurance();
        invoice.setRemark(StrUtil.format("入院科室：{}，出院科室：{}，入院日期：{}，出院日期：{}，医保类型：{}，医保统筹：{}，医保个账：{}，个人自费：{}",
                invoice.getDept(),
                inpatient.getOutDept(),
                DateUtil.format(inpatient.getInAt(), "yyyy-MM-dd"),
                DateUtil.format(inpatient.getOutAt(), "yyyy-MM-dd"),
                mi == null || mi.getType() == null ? "无" : mi.getType().title(),
                fundPay.getLeft(),
                fmtAmt(mi == null ? 0 : mi.getAccountPay()),
                fmtAmt(invoice.getOwnPay())
        ) + fundPay.getRight());
        return post(set(invoice));
    }

    static final Set<String> FUND_CODE_SET = new HashSet<>(4, 1) {{
        add("330100");
        add("610100");
        add("320100");
        add("390200");
    }};

    private Pair<BigDecimal, String> buildFundPay(Invoice invoice) {
        var mi = invoice.getMedicalInsurance();
        if (mi == null) {
            return Pair.of(BigDecimal.ZERO, "");
        }
        var fundPay = mi.getFundPay();
        var sb = new StringBuilder();
        try {
            var ls = listBySql("select b.Paid_Amount amt, b.Fund_Name name, b.Fund_Code code from microhis_mcisp.t_success_trans a, microhis_mcisp.t_trans_fund b where a.Trans_ID = b.Trans_ID and a.Cash_ID = " + invoice.getBizId());
            for (var map : ls) {
                if (FUND_CODE_SET.contains(map.getString("code"))) {
                    fundPay = NumberUtil.sub(fundPay, map.getBigDecimal("amt"));
                    sb.append("，").append(map.get("name")).append("：").append(fmtAmt(map.get("amt")));
                }
            }
        } catch (Exception e) {
            log.error("获取 TransFund 异常", e);
        }
        return Pair.of(fundPay, sb.toString());
    }

    private @Nullable Invoice post(OutpatientIn main) {
        try {
            Http.post(config(), main, "/cloud/v2/applyInvoice");
            return null;
        } catch (InvoiceHttpException e) {
            if (StrUtil.contains(e.getMessage(), "发票请求流水号已重复")) {
                return getNullable(main.getDdlsh());
            } else {
                throw e;
            }
        }
    }

    private OutpatientIn set(Invoice invoice) {
        var cfg = config();
        var details = $.toDetails(mapFeeType(), invoice);
        return OutpatientIn.builder()
                .qyId(cfg.companyId)
                .xsfMc(cfg.sellerName)
                .xsfDh(cfg.sellerMobile)
                .xsfNsrsbh(cfg.sellerOrgCode)
                .xsfYh(cfg.sellerBankAddress)
                .xsfZh(cfg.sellerBankAccount)
                .fplxdm("02")
                .zsfs("0")
                .qdbz("0")
                .kplx("0")
                .gmfzrrbs("Y")
                .dlzh($.getPayeeAccount(invoice.getPayeeId()))
                .bz(invoice.getRemark())
                .gmfMc(invoice.getName())
                .gmfMobile(invoice.getMobile())
                .gmfEmail(invoice.getEmail())
                .gmfNsrsbh(invoice.getCardNo())
                .kpr(invoice.getPayee())
                .sqr(invoice.getPayee())
                .spxxbcsm("1")
                .ddlsh(String.valueOf(invoice.getProviderKey()))
                .detailParam(details)
                .build();
    }

    private @Nullable Invoice getNullable(String providerKey) {
        var info = getInfo(providerKey);
        if (info == null) {
            return null;
        } else if (Objects.equals(info.status, "1")) {
            throw new BizException("请稍等！发票正在开具中……");
        } else if (!Objects.equals(info.status, "2")) {
            log.warn(info.ycyy);
            return null;
        } else {
            Invoice out = Invoice.builder()
                    .reversed(info.scarleted)
                    .url(info.jfdz)
                    .ticketAt($.toLocalDateTime(info.kprq))
                    .build();
            if (StrUtil.isNotBlank(info.qdfphm)) {
                out.setTicketCode(info.qdfphm.substring(0, 11));
                out.setTicketNo(info.qdfphm.substring(11));
            }
            return out;
        }
    }

    private @Nullable Info getInfo(String key) {
        var cfg = config();
        JSONArray array = Http.post(cfg, JSONObject.of("qyId", cfg.companyId, "ddlsh", key), "/cloud/v2/queryOrder");
        if (CollUtil.isEmpty(array)) {
            return null;
        }
        var json = array.getJSONObject(0);
        if (json == null) {
            return null;
        }
        var info = json.getJSONObject("fpxx").toJavaObject(Info.class);
        info.scarleted = CollUtil.isNotEmpty(json.getJSONArray("hzfpxx"));
        return info;
    }

    @Override
    public void scheduledTask() {
        log.info("发票查询任务开始执行");
    }
}