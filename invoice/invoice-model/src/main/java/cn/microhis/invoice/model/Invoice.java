package cn.microhis.invoice.model;

import cn.com.idmy.base.annotation.Column;
import cn.com.idmy.base.annotation.Id;
import cn.com.idmy.base.annotation.IdType;
import cn.com.idmy.base.annotation.Table;
import cn.microhis.invoice.model.enums.BizType;
import cn.microhis.invoice.model.enums.CardType;
import cn.microhis.invoice.model.enums.InvoiceStatus;
import cn.microhis.invoice.model.enums.PayerType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "invoiceId")
@Schema(title = "发票")
@Table(title = "发票", name = "invoice", schema = "mh_invoice")
public class Invoice {
    @Id(type = IdType.NONE)
    @Schema(title = "发票ID")
    private Long invoiceId;

    @Schema(title = "租户ID")
    @NotNull(message = "租户ID必填")
    private Long tenantId;

    @Schema(title = "租户")
    @Column(exist = false)
    private String tenant;

    @Schema(title = "供应商流水号")
    private String providerKey;

    @Schema(title = "票据代码")
    private String ticketCode;

    @Schema(title = "票据号码")
    private String ticketNo;

    @Schema(title = "票据日期")
    private LocalDateTime ticketAt;

    @Schema(title = "校验码")
    private String checkCode;

    @Schema(title = "已冲正")
    private Boolean reversed;

    @Schema(title = "开票状态")
    private InvoiceStatus status;

    @Schema(title = "金额")
    @NotNull(message = "发票金额不能为空")
    @Min(value = 0, message = "发票金额不能小于0")
    private BigDecimal amt;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "组ID")
    @NotNull(message = "发票分租必填")
    private Long groupId;

    @Schema(title = "业务ID")
    @NotNull(message = "业务ID必填")
    private Long bizId;

    @Schema(title = "业务类型")
    @NotNull(message = "业务类型必填")
    private BizType bizType;

    @Schema(title = "科室代码")
    @NotBlank(message = "科室代码必填")
    private String deptCode;

    @NotBlank(message = "科室必填")
    @Schema(title = "科室")
    private String dept;

    @Schema(title = "URL")
    private String url;

    @Schema(title = "卡类型")
    private CardType cardType;

    @Schema(title = "卡号")
    private String cardNo;

    @Schema(title = "付款人Id")
    private Long payerId;

    @Schema(title = "付款人类型")
    @NotNull(message = "付款人类型必填")
    private PayerType payerType;

    @Schema(title = "名称(个人=姓名，公司=公司名)")
    @NotNull(message = "名称必填")
    private String name;

    @Schema(title = "性别")
    private String gender;

    @Schema(title = "年龄")
    private String age;

    @Schema(title = "手机号")
    private String mobile;

    @Schema(title = "邮箱")
    private String email;

    @Schema(title = "个人自费", description = "现金/第三方支付，目录外，不报销，完全自担")
    private BigDecimal ownPay;

    @Schema(title = "现金支付")
    private BigDecimal cashPay;

    @Schema(title = "其他支付")
    private BigDecimal otherPay;

    @Schema(title = "收款人ID")
    private Long payeeId;

    @Schema(title = "收款人代码")
    @Column(exist = false)
    private String payeeCode;

    @Schema(title = "收款人")
    @NotBlank(message = "收款人必填")
    private String payee;

    @Schema(title = "复核人")
    private String checker;

    @Schema(title = "创建时间")
    private LocalDateTime createdAt;

    @Schema(title = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(title = "病历号")
    @NotBlank(message = "病历号必填")
    private String caseNo;

    @Valid
    @Schema(title = "门诊信息")
    @Column(exist = false)
    private Outpatient outpatient;

    @Valid
    @Schema(title = "住院信息")
    @Column(exist = false)
    private Inpatient inpatient;

    @Valid
    @Schema(title = "医保信息")
    @Column(exist = false)
    private MedicalInsurance medicalInsurance;

    @Valid
    @Column(exist = false)
    @Schema(title = "明细列表")
    @NotEmpty(message = "明细列表必填")
    private List<InvoiceDetail> details;
}