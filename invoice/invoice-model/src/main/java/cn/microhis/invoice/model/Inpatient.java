package cn.microhis.invoice.model;

import cn.com.idmy.base.annotation.Id;
import cn.com.idmy.base.annotation.IdType;
import cn.com.idmy.base.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "invoiceId")
@Schema(title = "住院发票")
@Table(title = "住院发票", name = "inpatient", schema = "mh_invoice")
public class Inpatient {
    @Id(type = IdType.NONE)
    @Schema(title = "发票ID")
    private Long invoiceId;

    @Schema(title = "住院号")
    @NotBlank(message = "住院号必填")
    private String no;

    @Schema(title = "住院天数")
    @NotNull(message = "住院天数必填")
    private Integer day;

    @Schema(title = "入院时间")
    @NotNull(message = "入院时间必填")
    private LocalDateTime inAt;

    @Schema(title = "出院科室代码")
    @NotBlank(message = "出院科室代码必填")
    private String outDeptCode;

    @Schema(title = "出院科室")
    @NotBlank(message = "出院科室必填")
    private String outDept;

    @Schema(title = "出院时间")
    @NotNull(message = "出院时间必填")
    private LocalDateTime outAt;

    @Schema(title = "退款金额")
    private BigDecimal refundAmt;

    @Schema(title = "预付金额")
    private BigDecimal prepayAmt;

    @Schema(title = "补缴金额")
    private BigDecimal rechargeAmt;
}