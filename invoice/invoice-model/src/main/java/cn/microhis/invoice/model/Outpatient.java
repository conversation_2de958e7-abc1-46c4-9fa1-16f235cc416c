package cn.microhis.invoice.model;


import cn.com.idmy.base.annotation.Id;
import cn.com.idmy.base.annotation.IdType;
import cn.com.idmy.base.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "invoiceId")
@Schema(title = "门诊发票")
@Table(title = "门诊发票", name = "outpatient", schema = "mh_invoice")
public class Outpatient {
    @Id(type = IdType.NONE)
    @Schema(title = "发票ID")
    private Long invoiceId;

    @Schema(title = "门诊号")
    @NotNull(message = "门诊号必填")
    private String no;

    @Schema(title = "就诊ID")
    @NotNull(message = "就诊ID必填")
    private Long visitId;

    @Schema(title = "就诊号")
    private String visitNo;

    @Schema(title = "就诊时间")
    @NotNull(message = "就诊时间必填")
    private LocalDateTime visitAt;
}