# 发票系统 (Invoice System)

## 项目概述

发票系统是一个完整的发票管理解决方案，支持多种发票类型（门诊、住院等）的创建、查询和冲正操作。系统集成了多个发票供应商插件，支持不同的发票服务提供商。

## 项目结构

项目采用多模块Maven结构，各模块职责如下：

- **invoice**: 根项目，包含所有子模块
    - **invoice-admin**: 管理后台接口
    - **invoice-api**: API接口（包含invoice-api-model和invoice-api-service子模块）
    - **invoice-base**: 基础功能模块
    - **invoice-model**: 数据模型定义
    - **invoice-provider**: 供应商接口定义
    - **invoice-plugins**: 供应商插件实现
        - invoice-plugins-baiwangjs: 百旺金赋插件
        - invoice-plugins-baiwangjf: 百旺金赋（另一版本）插件
        - invoice-plugins-bosssoft: 博思软件插件
    - **invoice-service**: 业务逻辑实现
    - **invoice-web**: Web接口
    - **invoice-web-starter**: Web应用启动模块

## 核心模型

### 发票相关模型

- **Invoice**: 发票主体，包含基本信息如发票ID、金额、状态等
- **InvoiceDetail**: 发票明细，包含项目名称、数量、单价等
- **InvoiceReversal**: 发票冲正记录
- **Outpatient**: 门诊发票信息
- **Inpatient**: 住院发票信息
- **MedicalInsurance**: 医保信息

### 其他模型

- **Provider**: 发票供应商信息，定义了供应商名称和对应的插件名称
- **ProviderTenant**: 租户与供应商的关联配置，包含供应商配置JSON
- **FeeType**: 费用类别，定义了费用代码、名称和供应商代码
- **ItemType**: 项目类别
- **Convert**: 转换表，用于不同系统间代码映射

## 数据库结构

系统使用MySQL数据库，主要表位于`mh_invoice`模式下：

- invoice: 发票主表
- invoice_detail: 发票明细表
- invoice_reversal: 发票冲正表
- outpatient: 门诊发票表
- inpatient: 住院发票表
- medical_insurance: 医保信息表
- provider: 供应商表
- provider_tenant: 租户供应商配置表
- fee_type: 费用类别表
- item_type: 项目类别表
- convert: 转换表

## 插件机制详解

### 插件架构

系统采用SPI（Service Provider Interface）机制实现插件的动态加载和管理：

1. **ProviderPlugin接口**: 所有供应商插件必须实现的核心接口，定义了发票创建、冲正等基本操作
2. **ProviderPluginBase抽象类**: 提供了插件的通用实现，简化插件开发
3. **PluginManager**: 负责插件的加载、初始化和管理
4. **PluginContext**: 提供线程上下文，存储当前操作的租户ID和机构类型

### 插件配置加载流程

1. 系统启动时，`PluginManager`通过Java SPI机制自动扫描classpath中实现了`ProviderPlugin`接口的类
2. 在`InvoiceConfig`中，系统将加载到的插件注册到`PluginManager`中，并设置必要的依赖
3. 当需要使用插件时，系统通过`InvoiceLogic.getProviderPlugin(tenantId)`方法获取插件：
    - 首先从`ProviderTenantService`获取租户对应的供应商配置(`ProviderTenant`)
    - 然后通过`PluginManager.getPlugin(providerTenant.getPluginName())`获取插件实例
    - 检查插件配置是否需要更新或初始化
    - 如需初始化，则调用`plugin.init(tenantId, providerTenant.getProviderConfig())`
4. 插件的`init`方法会调用`parseConfig`解析JSON配置，并将配置存储在内存中
5. 插件在执行业务操作时，通过`config()`方法获取当前租户的配置

### 插件使用流程

1. 业务层调用`InvoiceLogic.createProviderInvoice(invoice)`方法
2. 该方法获取对应的插件和租户配置
3. 使用`PluginContext.withContext()`设置当前线程的租户上下文
4. 调用插件的`create(invoice)`方法创建发票
5. 更新发票状态和相关信息

### 费用类别和项目类别

插件在处理发票时，通常需要访问费用类别(`FeeType`)和项目类别(`ItemType`)信息：

1. `ProviderPluginBase`类提供了`mapFeeType()`和`mapItemType()`方法，用于获取当前租户的费用类别和项目类别映射
2. 这些方法通过`FeeTypeRepository`和`ItemTypeRepository`接口获取数据
3. 插件可以使用这些映射将系统内部的费用代码转换为供应商特定的代码

### 多租户支持

系统支持多租户架构，不同租户可以配置不同的发票供应商：

1. 每个租户可以选择一个发票供应商
2. 租户的供应商配置存储在`ProviderTenant`表中的`providerConfig`字段（JSON格式）
3. 插件在处理发票时，会根据当前租户ID加载对应的配置

## 创建新插件

### 快速创建插件模板

要创建一个新的发票供应商插件，请按照以下步骤操作：

#### 1. 创建插件模块目录结构

```bash
# 在 invoice-plugins 目录下创建新插件目录
mkdir -p invoice/invoice-plugins/invoice-plugins-{供应商名称}/src/main/java/cn/microhis/invoice/plugins/{供应商名称}
mkdir -p invoice/invoice-plugins/invoice-plugins-{供应商名称}/src/main/resources/META-INF/services
```

#### 2. 创建 pom.xml 文件

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.microhis.invoice</groupId>
        <artifactId>invoice-plugins</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>invoice-plugins-{供应商名称}</artifactId>
    <dependencies>
        <dependency>
            <groupId>cn.microhis.invoice</groupId>
            <artifactId>invoice-provider</artifactId>
            <version>${parent.version}</version>
        </dependency>
    </dependencies>
</project>
```

#### 3. 创建插件实现类

```java
package cn.microhis.invoice.plugins.{供应商名称};

import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

@Slf4j
public class {供应商名称}Impl extends ProviderPluginBase<{供应商名称}Config> implements ProviderPlugin {

    public {供应商名称}Impl() {
        super("{供应商名称大写}");
    }

    @Override
    protected {供应商名称}Config parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        // TODO: 实现配置解析逻辑
        return new {供应商名称}Config();
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        // TODO: 实现门诊发票创建逻辑
        return invoice;
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        // TODO: 实现住院发票创建逻辑
        return invoice;
    }

    @Override
    protected Invoice createRegistration(Invoice invoice) {
        // TODO: 实现挂号发票创建逻辑
        return createOutpatient(invoice);
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice invoice) {
        // TODO: 实现发票冲红逻辑
        return null;
    }
}
```

#### 4. 创建配置类

```java
package cn.microhis.invoice.plugins.{供应商名称};

import lombok.Data;

@Data
public class {供应商名称}Config {
    // TODO: 添加供应商特定的配置字段
    String url;
    String appId;
    String appSecret;
}
```

#### 5. 创建 SPI 配置文件

在 `src/main/resources/META-INF/services/cn.microhis.invoice.provider.ProviderPlugin` 文件中添加：

```
cn.microhis.invoice.plugins.{供应商名称}.{供应商名称}Impl
```

#### 6. 更新父模块 pom.xml

在 `invoice-plugins/pom.xml` 的 `<modules>` 节点中添加：

```xml
<module>invoice-plugins-{供应商名称}</module>
```

### 示例：创建 Hebei 插件

以河北插件为例，完整的创建命令如下：

```bash
# 1. 创建目录结构
mkdir -p invoice/invoice-plugins/invoice-plugins-hebei/src/main/java/cn/microhis/invoice/plugins/hebei
mkdir -p invoice/invoice-plugins/invoice-plugins-hebei/src/main/resources/META-INF/services

# 2. 创建核心文件
touch invoice/invoice-plugins/invoice-plugins-hebei/pom.xml
touch invoice/invoice-plugins/invoice-plugins-hebei/src/main/java/cn/microhis/invoice/plugins/hebei/HebeiImpl.java
touch invoice/invoice-plugins/invoice-plugins-hebei/src/main/java/cn/microhis/invoice/plugins/hebei/HebeiConfig.java
touch invoice/invoice-plugins/invoice-plugins-hebei/src/main/resources/META-INF/services/cn.microhis.invoice.provider.ProviderPlugin

# 3. 创建 SPI 配置
echo "cn.microhis.invoice.plugins.hebei.HebeiImpl" > invoice/invoice-plugins/invoice-plugins-hebei/src/main/resources/META-INF/services/cn.microhis.invoice.provider.ProviderPlugin
```

### 插件开发要点

1. **继承 ProviderPluginBase**: 所有插件必须继承 `ProviderPluginBase<ConfigClass>` 并实现 `ProviderPlugin` 接口
2. **实现必要方法**: 必须实现 `parseConfig`、`createOutpatient`、`createInpatient`、`createRegistration`、`doReverse` 方法
3. **配置 SPI**: 在 `META-INF/services` 目录下正确配置 SPI 文件
4. **更新父模块**: 在父模块的 pom.xml 中添加新插件模块
5. **遵循命名规范**: 插件名称使用大写，包名使用小写

## 常见问题

1. **发票创建失败**: 检查供应商配置和网络连接
2. **插件初始化失败**: 确认供应商参数配置正确
3. **新插件不生效**: 检查SPI配置文件和依赖是否正确添加
4. **插件加载失败**: 确认插件类路径和包名正确
5. **配置解析错误**: 检查 parseConfig 方法中的配置项验证逻辑