package cn.microhis.invoice.provider;

import cn.com.idmy.base.exception.BizException;
import cn.microhis.invoice.model.FeeType;
import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.model.ItemType;
import cn.microhis.invoice.provider.exception.ExceptionLogger;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import cn.microhis.invoice.provider.exception.ProviderInitException;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.math.NumberUtil;
import org.jetbrains.annotations.Nullable;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Accessors(fluent = true)
@RequiredArgsConstructor
public abstract class ProviderPluginBase<CFG> implements ProviderPlugin {
    @Getter
    protected final String name;
    protected ConcurrentHashMap<Long, CFG> tenantConfig = new ConcurrentHashMap<>();
    protected ConcurrentHashMap<Long, LocalDateTime> tenantConfigUpdatedAt = new ConcurrentHashMap<>();
    @Setter
    protected ExceptionLogger exceptionLogger;

    @Override
    public boolean isInitialized(long tenantId) {
        return tenantConfig.containsKey(tenantId);
    }

    @Override
    public boolean checkConfigUpdate(long tenantId, LocalDateTime updatedAt) {
        boolean bol = updatedAt.equals(tenantConfigUpdatedAt.get(tenantId));
        tenantConfigUpdatedAt.put(tenantId, updatedAt);
        return bol;
    }

    protected String fmtAmt(Object value) {
        return NumberUtil.format("#.##", Optional.ofNullable(value).orElse(BigDecimal.ZERO));
    }

    @Override
    public void init(long tenantId, JSONObject config) {
        try {
            tenantConfig.put(tenantId, parseConfig(tenantId, config));
            log.info("插件 {} 成功初始化，租户ID: {}", name, tenantId);
        } catch (Exception e) {
            if (exceptionLogger != null) {
                exceptionLogger.logInitException(name, tenantId, e);
            }
            throw new ProviderInitException("初始化插件失败: " + e.getMessage(), e);
        }
    }

    protected abstract CFG parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException;

    protected CFG config() {
        long tenantId = PluginContext.getTenantId();
        CFG cfg = tenantConfig.get(tenantId);
        if (cfg == null) {
            throw new InvoiceConfigException("插件配置未初始化，租户ID: " + tenantId);
        } else {
            return cfg;
        }
    }

    @Override
    public Invoice create(Invoice invoice) {
        var startAt = System.currentTimeMillis();
        if (invoice.getProviderKey() == null) {
            invoice.setProviderKey(IdUtil.getSeataSnowflakeNextIdStr());
        }
        try {
            log.info("开始创建发票，提供商: {}, 供应商流水号：{}, 业务类型：{}，业务ID: {}", name, invoice.getProviderKey(), invoice.getBizType(), invoice.getBizId());
            Invoice out = switch (invoice.getBizType()) {
                case REG -> createReg(invoice);
                case OUTPATIENT -> createOutpatient(invoice);
                case INPATIENT -> createInpatient(invoice);
                case OTHER -> createOther(invoice);
            };
            log.info("创建发票成功，提供商: {}, 供应商流水号：{}, 业务类型：{}，业务ID: {}, 耗时: {}ms", name, invoice.getProviderKey(), invoice.getBizType(), invoice.getBizId(), System.currentTimeMillis() - startAt);
            return out;
        } catch (Exception e) {
            if (exceptionLogger != null) {
                exceptionLogger.logBusinessException(name, "create", invoice.getBizId(), e);
            }
            log.error("创建发票失败，提供商: {}, 供应商流水号：{}, 业务类型：{}， 业务ID: {}, 错误: {}", name, invoice.getProviderKey(), invoice.getBizType(), invoice.getBizId(), e.getMessage(), e);
            throw e;
        }
    }

    protected Invoice createReg(Invoice invoice) {
        return createOutpatient(invoice);
    }

    protected abstract Invoice createOutpatient(Invoice invoice);

    protected abstract Invoice createInpatient(Invoice invoice);

    protected Invoice createOther(Invoice invoice) {
        throw new BizException("不支持的开票类型");
    }
    protected Invoice createOther(Invoice invoice) {
        throw new BizException("不支持的开票类型");
    }

    @Override
    public @Nullable InvoiceReversal reverse(Invoice invoice) {
        var startAt = System.currentTimeMillis();
        try {
            log.info("开始冲红发票，提供商: {}, 业务ID: {}", name, invoice.getBizId());
            var reversal = doReverse(invoice);
            log.info("冲红发票成功，提供商: {}, 业务ID: {}, 耗时: {}ms", name, invoice.getBizId(), System.currentTimeMillis() - startAt);
            return reversal;
        } catch (Exception e) {
            if (exceptionLogger != null) {
                exceptionLogger.logBusinessException(name, "reverse", invoice.getBizId(), e);
            }
            log.error("冲红发票失败，提供商: {}, 业务ID: {}, 错误: {}", name, invoice.getBizId(), e.getMessage(), e);
            throw e;
        }
    }

    protected abstract @Nullable InvoiceReversal doReverse(Invoice invoice);

    @Setter
    private FeeTypeRepository feeTypeRepository;
    @Setter
    private ItemTypeRepository itemTypeRepository;
    @Setter
    private JdbcTemplate jdbcTemplate;

    protected Map<String, FeeType> mapFeeType() {
        return feeTypeRepository.mapByTenantId(PluginContext.getTenantId());
    }

    protected Map<String, ItemType> mapItemType() {
        return itemTypeRepository.mapByTenantId(PluginContext.getTenantId());
    }

    protected JSONObject getBySql(String sql) {
        return jdbcTemplate.queryForObject(sql, JSONObject.class);
    }

    protected List<JSONObject> listBySql(String sql) {
        return jdbcTemplate.queryForList(sql, JSONObject.class);
    }
}