<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.microhis.bcs.dao.CashDao">
    <update id="updateInsuranceType">
        update microhis_bcs.t_cash c
            join microhis_bcs.t_cash_trans ct on ct.cash_id = c.cash_id and ct.trans_status = 3 and ct.provider_id = 4 and ct.mi_trans_id is not null
            join microhis_mcisp.t_trans t on t.trans_id = ct.mi_trans_id
        set c.insurance_type_id = t.insurance_type_id
        where c.insurance_type_id is null
          and c.validated_flag = 1
          and c.cash_id = #{cashId}
    </update>

    <select id="pageFullCash" resultType="cn.microhis.bcs.model.out.CashOut">
        select
        t_visit.patient_id as payer_id,
        t_user_code.user_name as payee,
        t_clinician.clinician_name as clinician_name,
        t_insurance_type.insurance_name,
        t_cash.*
        from microhis_bcs.t_cash
        left join microhis_hsd.t_visit on t_cash.visit_id = t_visit.visit_id
        left join hip_mdi.t_clinician on t_clinician.clinician_id = t_visit.clinician_id
        left join hip_mdi.t_insurance_type on t_cash.insurance_type_id = t_insurance_type.insurance_type_id
        left join hip_mdi.t_user_code on t_cash.user_id = t_user_code.user_id
        <where>
            <if test="where != null">${where}</if>
            <if test="params.clinicianName != null and params.clinicianName != ''">
                and t_clinician.clinician_name like concat('%', #{params.clinicianName} ,'%')
            </if>
            <if test="params.payee != null and params.payee != ''">
                and t_user_code.user_name like concat('%', #{params.payee} ,'%')
            </if>
            <if test="params.payer != null and params.payer != ''">
                and t_visit.Patient_Name like concat('%', #{params.payer} ,'%')
            </if>
            <if test="params.paymentType != null">
                and exists (select 1 from microhis_bcs.t_cash_payment where cash_id = t_cash.cash_id and status = 3 and payment_id = #{params.paymentType.value})
            </if>
        </where>
        <if test="orderBy != null">order by ${orderBy}</if>
    </select>

    <select id="countCash" resultType="cn.microhis.bcs.model.out.CashOut">
        <![CDATA[
        select
            sum(t_cash.presettle_amount) as total_amount,
            sum(case when t_cash.amount > 0 then t_cash.amount else 0 end) as amount,
            sum(case when t_cash.amount < 0 then t_cash.amount else 0 end) as refunded_amount,
            sum(t_cash.derated) as derated,
            sum(t_cash.discounted) as discounted,
            count(t_cash.cash_id) as cash_count
        from microhis_bcs.t_cash
             left join microhis_hsd.t_visit t_visit on t_cash.visit_id = t_visit.visit_id
             left join hip_mdi.t_clinician on t_clinician.clinician_id = t_visit.clinician_id
             left join hip_mdi.t_user_code on t_cash.user_id = t_user_code.user_id
        ]]>
        <where>
            <if test="where != null">${where}</if>
        </where>
    </select>
    <select id="sumMiAmt" resultType="cn.microhis.bcs.model.out.CashOut">
        select
        sum(mi_fund_amt) as mi_fund_amt,
        sum(mi_acct_amt) as mi_acct_amt,
        sum(family_acct_amt) as family_acct_amt
        from microhis_bcs.t_cash_trans
        inner join microhis_bcs.t_cash on t_cash_trans.cash_id = t_cash.cash_id and provider_id = 4 and trans_status = 3
        inner join hip_mdi.t_user_code on t_cash.user_id = t_user_code.user_id
        inner join microhis_hsd.t_visit t_visit on t_cash.visit_id = t_visit.visit_id
        inner join hip_mdi.t_clinician on t_clinician.clinician_id = t_visit.clinician_id
        <where>
            <if test="where != null">${where}</if>
        </where>
    </select>
</mapper>
